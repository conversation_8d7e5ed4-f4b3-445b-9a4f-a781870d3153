function isPalindrome(x: number): boolean {
  const str = x.toString();
  const reverseX = str.split("").reverse().join("");
  return x.toString() === reverseX;
}
function isPalindromeV2(x: number): boolean {
  if (x < 0) return false;
  let copyX = x;
  let reverse = 0;
  while (copyX > 0) {
    const num = copyX % 10;
    console.log(num);
    reverse = reverse * 10 + num;
    copyX = Math.floor(copyX / 10);
  }
  return x === reverse;
}
isPalindrome(121);
isPalindromeV2(121);
