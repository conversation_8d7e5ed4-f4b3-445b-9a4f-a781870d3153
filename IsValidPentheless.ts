function isValid(s: string): boolean {
  const stack: string[] = [];
  const map: { [key: string]: string } = {
    ")": "(",
    "}": "{",
    "]": "[",
  };
  console.log(map[")"]);
  for (let char of s) {
    if (char === ")" || char === "}" || char === "]") {
        const top = stack.pop()
        
        if(top !== map[char]){
            return false
        }
    }else{
        stack.push(char)
    }
  }
  return stack.length === 0;
}
isValid("()[]{}");
