function longestCommonPrefix(strs: string[]): string {
  if (strs.length < 0) return "";
  for (let i = 0; i < strs[0].length; i++) {
    for (let j = 1; j < strs.length; j++) {
      if (i > strs[j].length || strs[j][i] !== strs[0][i]) {
        console.log("a0", strs[0].slice(0, i));
      }
    }
  }
  console.log("aaa", strs[0]);
  return strs[0];
}
longestCommonPrefix(["flower", "flow", "flight"]);
